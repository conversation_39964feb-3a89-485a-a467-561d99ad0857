import { NextRequest, NextResponse } from 'next/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

let supabase: SupabaseClient | null = null;
if (supabaseUrl && supabaseKey) {
  supabase = createClient(supabaseUrl, supabaseKey, {
    db: { schema: 'public' },
  });
}

// Define attractiveness factors and their corresponding product keywords
const ATTRACTIVENESS_KEYWORD_MAPPING: Record<string, string[]> = {
  'skin_radiance': ['skin_radiance', 'vitamin_c_glow', 'complexion_evenskin', 'skin_brightness'],
  'skin_elasticity': ['skin_elasticity', 'skin_firmness', 'collagen_support', 'facial_contouring'],
  'bone_structure': ['bone_density', 'facial_bone_structure', 'jawline_definition', 'mineral_absorption'],
  'facial_symphony': ['facial_harmony', 'facial_symmetry', 'face_balance', 'proportional_features'],
  'eye_vitality': ['eye_vitality', 'ocular_freshness', 'vision_brightness', 'iris_color_enhancement'],
  'hair_health': ['hair_support', 'hair_thickness', 'scalp_health', 'hair_follicle_nutrition'],
  'undefined': ['skin_radiance', 'collagen_support', 'facial_bone_structure', 'eye_vitality']
};

// Extract attractiveness factors from analysis recommendations
function extractAttractivenessFactors(recommendations: string): string[] {
  const lowerText = recommendations.toLowerCase();
  const factors: string[] = [];

  // Map analysis text to attractiveness factors
  if (lowerText.includes('skin') || lowerText.includes('hydration') || lowerText.includes('texture')) {
    factors.push('skin_radiance', 'skin_elasticity');
  }

  if (lowerText.includes('bone') || lowerText.includes('structure') || lowerText.includes('facial') || lowerText.includes('jaw')) {
    factors.push('bone_structure');
  }

  if (lowerText.includes('eye') || lowerText.includes('vision') || lowerText.includes('ocular')) {
    factors.push('eye_vitality');
  }

  if (lowerText.includes('hair') || lowerText.includes('growth') || lowerText.includes('thickness')) {
    factors.push('hair_health');
  }

  if (lowerText.includes('symmetry') || lowerText.includes('proportional')) {
    factors.push('facial_symphony');
  }

  // Default factors if none matched
  if (factors.length === 0) {
    factors.push('skin_radiance', 'bone_structure', 'eye_vitality');
  }

  return factors;
}

// Get relevant product keywords based on attractiveness factors
function getRelevantKeywords(factors: string[]): string[] {
  const keywords: string[] = [];
  const keywordSet = new Set<string>();

  factors.forEach(factor => {
    const factorKeywords = ATTRACTIVENESS_KEYWORD_MAPPING[factor] || [];
    factorKeywords.forEach(keyword => keywordSet.add(keyword));
  });

  // Convert set to array
  const uniqueKeywords: string[] = [];
  keywordSet.forEach(keyword => uniqueKeywords.push(keyword));
  return uniqueKeywords;
}

export async function POST(request: NextRequest) {
  try {
    const { recommendations } = await request.json();

    if (!recommendations || typeof recommendations !== 'string') {
      return NextResponse.json(
        { error: 'Recommendations text is required' },
        { status: 400 }
      );
    }

    // Extract attractiveness factors from recommendations
    const factors = extractAttractivenessFactors(recommendations);
    const keywords = getRelevantKeywords(factors);

    // Check if Supabase is configured
    if (!supabase) {
      // Return sample products for demo purposes when database isn't configured
      const sampleProducts = [
        {
          asin: 'DEMO-001',
          title: 'Mega Collagen Plus Vitamin C Hyaluronic Acid for Skin & Joints Support',
          imageUrl: 'https://m.media-amazon.com/images/I/61-LYQGtQCL._AC_UL320_.jpg',
          price: '$29.99',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 1248,
          affiliateUrl: `https://www.amazon.com/dp/B07WZVX3L1?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['skin_radiance', 'vitamin_c_glow', 'collagen_support'],
          isDiscounted: false
        },
        {
          asin: 'DEMO-002',
          title: 'Triple Strength Omega 3 Fish Oil Supplement EPA DHA',
          imageUrl: 'https://m.media-amazon.com/images/I/71ZBnxvlvCL._AC_UL320_.jpg',
          price: '$34.95',
          priceBeforeDiscount: '$39.99',
          rating: null,
          reviewCount: 326,
          affiliateUrl: `https://www.amazon.com/dp/B01NBTJFJB?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['eye_vitality', 'omega3_antioxidants'],
          isDiscounted: true
        },
        {
          asin: 'DEMO-003',
          title: 'Sports Research Multi Collagen Peptides with Hyaluronic Acid',
          imageUrl: 'https://m.media-amazon.com/images/I/514DekriBLL._AC_UL320_.jpg',
          price: '$59.95',
          priceBeforeDiscount: null,
          rating: null,
          reviewCount: 1856,
          affiliateUrl: `https://www.amazon.com/dp/B07C7S9Q4F?tag=amzleanmed-20`,
          category: 'skin_care',
          semanticKeywords: ['bone_density', 'facial_bone_structure', 'collagen_support'],
          isDiscounted: false
        }
      ];

      return NextResponse.json({
        products: sampleProducts,
        totalSelected: 3,
        factorsUsed: factors,
        message: 'Using demo products - database not configured'
      });
    }

    // Query products that match the semantic keywords
    const { data: products, error } = await supabase
      .from('amazon_product_references')
      .select('*')
      .overlaps('semantic_keywords', keywords)
      .limit(6); // Get a few extra to randomize

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch product recommendations' },
        { status: 500 }
      );
    }

    // Randomly select 3 products to keep variety
    const shuffled = products?.sort(() => 0.5 - Math.random()) || [];
    const selectedProducts = shuffled.slice(0, 3);

    // Transform to match expected format with affiliate links
    const curatedProducts = selectedProducts.map(product => ({
      asin: product.product_id,
      title: product.product_name,
      imageUrl: product.product_image_url,
      price: `$${(product.product_price || 0).toString()}`,
      priceBeforeDiscount: product.product_price_before_discount ? `$${(product.product_price_before_discount).toString()}` : null,
      rating: null, // We don't have rating data in our table
      reviewCount: product.review_count,
      affiliateUrl: `https://www.amazon.com/dp/${product.product_id}?tag=amzleanmed-20`,
      category: product.primary_category,
      semanticKeywords: product.semantic_keywords,
      isDiscounted: product.is_discounted
    }));

    return NextResponse.json({
      products: curatedProducts,
      totalSelected: curatedProducts.length,
      factorsUsed: factors
    });

  } catch (error) {
    console.error('Curated products error:', error);
    return NextResponse.json(
      { error: 'Failed to generate product recommendations' },
      { status: 500 }
    );
  }
}
