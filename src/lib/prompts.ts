export const PROMPTS = {
  smileAnalysis: `First, verify this is a clear photo of a human face with a visible smile. Look for:
  - Human facial features (eyes, nose, mouth)
  - Natural skin texture
  - Clear facial structure
  - Visible smile expression
  
  If ANY of these are missing or if the image shows non-human subjects (animals, objects, artwork, etc.), respond with this exact JSON:
  {
    "error": "Please upload a human face to begin"
  }

  If a human face with a smile is detected, analyze the smile and provide a detailed assessment. Focus ONLY on these EXACT factors in this EXACT order:
  1. Genuineness - How authentic and natural the smile appears
  2. Symmetry - The balance and alignment of facial features during the smile
  3. Teeth Appearance - The visibility, alignment, and whiteness of teeth
  4. Facial Proportions - How the smile affects overall facial harmony
  5. Confidence - The level of self-assurance conveyed by the smile
  6. Emotional Resonance - How effectively the smile conveys positive emotions

  IMPORTANT RULES:
  1. Overall score must include one decimal place (e.g., 7.8, 6.4, not whole numbers)
  2. Use "you" and "your" in descriptions and recommendations (not "the subject" or "they")
  3. Only suggest natural improvements (NO surgery or medical procedures)
  4. Keep recommendations practical and achievable
  5. Maintain a positive, encouraging tone
  6. All scores must be between 5 and 10 (no scores below 5)
  7. Make recommendations specific to what you observe in the image
  8. Base recommendations on actual features visible in the photo
  9. Highlight existing strengths while suggesting subtle enhancements

  For each factor, provide:
  - A personal analysis using "you/your"
  - Recommendations that:
    1. Start with a varied positive acknowledgment (mix these styles):
       - "Your smile is..."
       - "What stands out is..."
       - "I notice that..."
       - "The photo shows..."
       - "A notable strength is..."
    2. Then provide specific, natural suggestions for enhancement
    3. Keep suggestions actionable and achievable
    4. Focus on building upon existing strengths

  Return JSON in this format:
  {
    "overall_score": number (must include one decimal place, e.g., 7.8, 6.4),
    "factors": [
      {
        "name": "Genuineness",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Symmetry",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Teeth Appearance",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Facial Proportions",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Confidence",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Emotional Resonance",
        "score": number,
        "description": string,
        "recommendation": string
      }
    ],
    "analysis_text": string (Use "you" and "your" instead of "her/him/they" or "the image shows". For example: "You have a beautiful face with pleasing features. Your well-maintained skin quality and facial proportions create an attractive appearance with warmth. You present a well-balanced and harmonious face with naturally pleasant features."),
    "overall_recommendations": string (Start with varied positive acknowledgments of existing features, then recommend natural supplement-based enhancements tailored to the specific facial analysis factors)
  }`,
  
  howAttractiveAmI: `First, verify this is a clear photo of a human face. Look for:
  - Human facial features (eyes, nose, mouth)
  - Natural skin texture
  - Clear facial structure
  - Recognizable human face
  
  If ANY of these are missing or if the image shows non-human subjects (animals, objects, artwork, etc.), respond with this exact JSON:
  {
    "error": "Please upload a human face to begin"
  }

  If a human face is detected, analyze the facial image and provide a comprehensive attractiveness assessment with natural, non-surgical recommendations for improvement. Focus ONLY on these EXACT 9 factors in this EXACT order (grouped by looksmax priority):

  **HIGH PRIORITY (Bone Structure & Genetics):**
  1. Facial Symmetry - The balance and alignment of facial features (most fundamental)
  2. Jawlines - The definition, contour, and shape of the jawline (crucial for masculine appeal)
  3. Cheekbone - The prominence, definition, and facial structure around cheekbones
  4. Face Shape Detector - The overall face shape proportions and balance

  **MEDIUM PRIORITY (Improvable Features):**
  5. Eyes - The shape, size, expression, and aesthetic appeal of the eyes
  6. Eyebrow Shape - The shape, thickness, and arch of eyebrows (easily improvable)
  7. Skin Quality - The clarity, texture, smoothness, and overall health of the skin
  8. Masculinity - Gender-specific facial features and their perceived attractiveness

  **LOWER PRIORITY (Style & Presentation):**
  9. Hairstyle - The hair length, cut, styling, and how it frames the face (easily changeable)

  IMPORTANT RULES:
  1. Overall score must include one decimal place (e.g., 7.8, 6.4, not whole numbers)
  2. Use "you" and "your" in descriptions and recommendations (not "the subject" or "they")
  2. Only suggest natural improvements (NO surgery, professional consultations, or medical procedures)
  3. Focus on nutritional supplements that support skin health, hair quality, bone structure, and facial appearance
  4. Keep recommendations practical and achievable
  5. Maintain a positive, encouraging tone
  6. All scores must be between 5 and 10 (no scores below 5)
  7. Make recommendations specific to what you observe in the image, not generic advice
  8. Base recommendations on actual features and characteristics visible in the photo
  9. Highlight existing strengths while suggesting subtle enhancements
  10. Provide specific, actionable tips based on the person's unique features

  For each factor, provide:
  - A personal analysis using "you/your"
  - Recommendations that:
    1. Start with a varied positive acknowledgment (mix these styles):
       - "Your [feature] is..."
       - "What stands out is..."
       - "I notice that..."
       - "The photo shows..."
       - "A notable strength is..."
    2. Then provide specific, natural suggestions for enhancement
    3. Keep suggestions actionable and achievable
    4. Focus on building upon existing strengths

  Return JSON in this format:
  {
    "overall_score": number (must include one decimal place, e.g., 7.8, 6.4),
    "factors": [
      {
        "name": "Facial Symmetry",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Jawlines",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Cheekbone",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Face Shape Detector",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Eyes",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Eyebrow Shape",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Skin Quality",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Masculinity",
        "score": number,
        "description": string,
        "recommendation": string
      },
      {
        "name": "Hairstyle",
        "score": number,
        "description": string,
        "recommendation": string
      }
    ],
    "analysis_text": string (Use "you" and "your" instead of "her/him/they" or "the image shows". For example: "You have a warm and genuine smile that lights up your face. Your natural expression and balanced features create an inviting and friendly appearance. You present a confident and approachable demeanor through your smile."),
    "overall_recommendations": string (Start with varied positive acknowledgments of existing features, then recommend natural supplement-based enhancements tailored to the specific facial analysis factors)
  }`,

  faceShapeDetector: `First, verify this is a clear photo of a human face. Look for:
  - Human facial features (eyes, nose, mouth)
  - Natural skin texture
  - Clear facial structure

  If ANY of these are missing or if the image shows non-human subjects (animals, objects, artwork, etc.), respond with this exact JSON:
  {
    "error": "Please upload a human face to begin"
  }

  If a human face is detected, analyze the facial shape from the following common categories:
  1. Oval - The most balanced face shape; widest at the cheekbones, slightly narrower at the jaw
  2. Round - Equal width and length, with fuller cheeks and a rounded jawline
  3. Square - Strong jawline, broad forehead, equal width measurements throughout
  4. Heart - Wider forehead and cheekbones, narrow chin, tapering towards the bottom
  5. Diamond - Narrow forehead, wide cheekbones, narrow chin
  6. Triangle - Narrow forehead, wide jawline, angular features
  7. Oblong - Longer face, equal width throughout (rectangle-like)

  IMPORTANT RULES:
  1. Overall score must include one decimal place (e.g., 7.8)
  2. Use "you" and "your" in descriptions
  3. Provide specific measurements and ratios where possible
  4. Maintain a positive, encouraging tone
  5. All scores must be between 5 and 10

  For modern and complementary styling recommendations, consider current fashion trends including but not limited to:
  - For Oval faces: Balanced makeup, versatile hairstyles
  - For Round faces: Angular cuts, lifted hairstyles, elongated makeup
  - For Square faces: Soft waves, rounded features, softer makeup
  - For Heart faces: Chin-length cuts, side-swept bangs, balanced makeup
  - For Diamond faces: Chin-length or longer layers, wispy bangs
  - For Triangle faces: Full hair near the jaw, bangs, bold eye makeup
  - For Oblong faces: Soft waves, layers, face-framing styles

  Return JSON in this format:
  {
    "shape": string (one of: "Oval", "Round", "Square", "Heart", "Diamond", "Triangle", "Oblong"),
    "confidence_score": number (0-1, how confident the AI is),
    "measurements": {
      "face_length_ratio": number (1.0 for perfectly proportional),
      "cheekbone_width_ratio": number,
      "jawline_angle": string ("sharp", "soft", "rounded"),
      "forehead_width": string ("narrow", "average", "wide")
    },
    "description": string (Use "you" and "your", describe the shape characteristics),
    "styling_tips": [
      {
        "category": "Hairstyles",
        "tips": string[]
      },
      {
        "category": "Makeup",
        "tips": string[]
      },
      {
        "category": "Glasses",
        "tips": string[]
      }
    ],
    "celebrity_examples": string[] (2-3 celebrities with similar face shapes),
    "overall_score": number (how desirable/attractive the shape is, 5-10 scale),
    "analysis_text": string (positive analysis using "you" and "your")
  }`
}
